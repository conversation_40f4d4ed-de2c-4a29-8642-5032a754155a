@import '~@paytm-h5-common/paytm_common_ui/styles/colors';
@import '../utils/_darkVars.scss';

:root {
  --background-widget-blue: #d1e2eb;
  --background-offset-weak: rgba(28, 28, 28, 0.05);
  --background-offset-medium: rgba(28, 28, 28, 0.1);
  --text-neutral-strong: #1c1c1c;
  --grey900: #333333;
  --text-neutral-medium: #505050;
  --text-neutral-weak: #707070;
  --text-positive-strong: #02a85d;
  --text-negative-strong: #b74040;
  --plain: #ffffff;
  --categoryText: #1010108a;
  --background-offset-strong: #e0e0e0;
  --background-fund-light: #eeeeee;
  --background-fund-dark: #000000;
  --Text-grey500: #1010108a;
  --Text-grey900: #eeeeee8a;
  --Border-grey100: #1010100f;
  --Border-grey200: #10101021;
}

@media (prefers-color-scheme: dark) {
  html:not(.miniapp) :root {
    @include darkVars;
    --text-neutral-strong-dark: var(--grey900);
    --text-neutral-weak-dark: var(--grey600);
    --background-offset-weak-dark: var(--grey100);
    --background-offset-medium-dark: var(--grey200);
  }
}

@mixin typography(
  $size,
  $color: map-get($colors, primaryTextColor),
  $fontFamily: Inter
) {
  // B corresponds to font-weight bold or 700, B1 to 600, B2 to 500, default is 400.
  // R corresponds to regular text with lineHeight 1.4.
  @if $size == title1B {
    font-size: 42px;
    font-weight: 700;
    line-height: 1;
  }
  @if $size == title1B1 {
    font-size: 32px;
    font-weight: 700;
    line-height: 1;
  }
  @if $size == title2B {
    font-size: 30px;
    font-weight: 700;
    line-height: 0.87;
  }
  @if $size == heading286136 {
    font-size: 28px;
    font-weight: 600;
    line-height: 1.36;
  }
  @if $size == heading266136 {
    font-size: 26px;
    font-weight: 600;
    line-height: 1.36;
  }
  @if $size == heading1B1 {
    font-size: 26px;
    font-weight: 600;
    line-height: 1.46;
  }
  @if $size == heading1B3 {
    font-size: 24px;
    font-weight: 600;
    line-height: 1.5;
  }
  @if $size == heading1B4 {
    font-size: 24px;
    font-weight: 700;
  }
  @if $size == heading1B2 {
    font-size: 22px;
    font-weight: 700;
  }
  @if $size == heading2 {
    font-size: 20px;
    line-height: 1.1;
  }
  @if $size == heading2R {
    font-size: 20px;
    line-height: 1.4;
  }
  @if $size == heading2B {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.5;
  }
  @if $size == heading2B1 {
    font-size: 20px;
    font-weight: 600;
    line-height: 1.5;
  }
  @if $size == heading2B2 {
    font-size: 20px;
    font-weight: 500;
    line-height: 26px;
  }
  @if $size == heading5B {
    font-size: 18px;
    font-weight: 700;
    line-height: 1.5;
  }
  @if $size == heading5B1 {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.5;
  }
  @if $size == heading5B2 {
    font-size: 18px;
    font-weight: 500;
    line-height: 1.5;
  }

  @if $size == heading3 {
    font-size: 16px;
    line-height: 1.5;
  }
  @if $size == heading3B {
    font-size: 16px;
    font-weight: 700;
    line-height: 1.5;
  }
  @if $size == heading3B1 {
    font-size: 16px;
    font-weight: 600;
    line-height: 1.5;
  }
  @if $size == heading3B2 {
    font-size: 16px;
    font-weight: 500;
  }
  @if $size == heading3B3 {
    font-size: 16px;
  }

  @if $size == heading14 {
    font-size: 14px;
    line-height: 1.5;
  }

  @if $size == heading4B {
    font-size: 14px;
    font-weight: 700;
    line-height: 1.29;
  }
  @if $size == heading4B1 {
    font-size: 14px;
    font-weight: 600;
    line-height: 1.43;
  }

  @if $size == text {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.43;
  }
  @if $size == text2 {
    font-size: 14px;
  }

  @if $size == body1R {
    font-size: 14px;
    line-height: 1.43;
  }
  @if $size == body1R1 {
    font-size: 14px;
    line-height: 2.14;
  }
  @if $size == body2 {
    font-size: 12px;
    line-height: 1.5;
  }
  @if $size == body2B {
    font-size: 12px;
    font-weight: 700;
    line-height: 1.5;
  }
  @if $size == body2B1 {
    font-size: 12px;
    font-weight: 600;
    line-height: 1.5;
  }
  @if $size == body2B2 {
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
  }
  @if $size == body2B3 {
    font-size: 12px;
    font-weight: 400;
  }
  @if $size == body2B4 {
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
  }
  @if $size == body2R {
    font-size: 12px;
    line-height: 1.42;
  }
  @if $size == body2R1 {
    font-size: 12px;
    line-height: 1.17;
  }
  @if $size == body2R2 {
    font-size: 12px;
    line-height: 2.5;
  }
  @if $size == body2R3 {
    font-size: 12px;
    line-height: 1.5;
  }
  @if $size == body2R4 {
    font-size: 11px;
    line-height: 14px;
    font-weight: 500;
  }
  @if $size == body3R {
    font-size: 10px;
    line-height: 1.4;
  }
  @if $size == body3R1 {
    font-size: 10px;
    line-height: 1.8;
  }
  @if $size == body3R3 {
    font-size: 10px;
    line-height: 1.6;
  }
  @if $size == body3R2 {
    font-size: 10px;
    line-height: 1.3;
  }
  @if $size == body3B2 {
    font-size: 10px;
    font-weight: 500;
    line-height: 1.7;
  }
  @if $size == body3B3 {
    font-size: 10px;
    font-weight: 600;
  }
  @if $size == body3B4 {
    font-size: 10px;
    font-weight: 600;
    line-height: 12.1px;
  }

  @if $color {
    color: $color;
  }

  @if $fontFamily {
    font-family: $fontFamily;
  }
}
