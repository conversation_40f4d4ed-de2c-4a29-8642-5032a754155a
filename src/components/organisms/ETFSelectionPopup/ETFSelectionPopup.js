import React, { useState, useRef, useEffect } from 'react';

import InfiniteScroll from '../../molecules/InfiniteScroll';
import Drawer from '../../molecules/Drawer/Drawer';
import { isPaytmMoney } from '../../../utils/coreUtil';
import { exitApp } from '../../../utils/bridgeUtils';
import { useBackPress } from '../../../hooks/useNativeBackPress';
import { useETFAnalyticsEvent } from '../../../hooks/useETFAnalyticsEvent';

import styles from './ETFSelectionPopup.scss';
import { LABELS, PULSE_STATICS } from './enums';
import ETFCard from './ETFCard';
import { PRESELECTED_ETF } from '../../../pages/ETFCard/enums';

const ETFSelectionPopup = ({
  isOpen = false,
  preSelectedETF,
  setEtf = () => {},
  onClose,
  etfData: { popularEtfs, returns = [], widgetId, subCohortId },
}) => {
  const [etfList, setEtfList] = useState([]);
  const [selectedEtf, setSelectedEtf] = useState(null);
  const infiniteScrollRef = useRef(null);

  const { sendAnalyticsEventETF } = useETFAnalyticsEvent({
    widgetId: widgetId || '',
    subCohortId: subCohortId || '',
  });
  const { stack } = useBackPress();

  useEffect(() => {
    setEtfList(popularEtfs);
    if (isOpen && preSelectedETF) {
      setEtfList(popularEtfs);
      setSelectedEtf(preSelectedETF);
    }
  }, [isOpen, popularEtfs, preSelectedETF]);

  const closePopup = () => {
    if (isPaytmMoney()) {
      localStorage.setItem(PRESELECTED_ETF, JSON.stringify(selectedEtf));
      exitApp();
    } else {
      setEtf(selectedEtf);
      onClose();
      stack.pop();
    }

    sendAnalyticsEventETF({
      action: PULSE_STATICS.ACTION.SELECT_ETF_CLICK,
      label: selectedEtf.name,
    });
  };

  const onPopupClose = () => {
    onClose();
  };

  const handleSelect = (etf) => {
    sendAnalyticsEventETF({
      action: PULSE_STATICS.ACTION.ETF_SELECT,
      label: etf.name,
    });
    setSelectedEtf(etf);
  };

  return (
    <Drawer
      active={isOpen}
      triggerClose={onPopupClose}
      showGrabber
      showCloseIcon={false}
      primaryButton={{ label: LABELS.POPUP_CTA_TEXT, onClick: closePopup }}
      customClass={styles.etfSelectionPopupContainer}
    >
      <h1 className={styles.title}>{LABELS.TITLE}</h1>
      <div className={styles.scrollContainer} ref={infiniteScrollRef}>
        <InfiniteScroll
          pageStart={1}
          loadMore={() => {}}
          useWindow={false}
          useCapture={false}
          threshold={500}
          className={styles.etfList}
          getScrollParent={() => infiniteScrollRef.current}
        >
          {etfList.map((etf) => (
            <ETFCard
              key={etf.id}
              etf={etf}
              isSelected={selectedEtf?.id === etf.id}
              onSelect={handleSelect}
              returns={returns}
            />
          ))}
        </InfiniteScroll>
      </div>
    </Drawer>
  );
};

export default ETFSelectionPopup;
