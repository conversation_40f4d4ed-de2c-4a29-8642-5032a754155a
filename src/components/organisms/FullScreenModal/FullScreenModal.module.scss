.full-screen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-default2);
  z-index: 1000;
  box-sizing: border-box;

  &.darkMode {
    background-color: var(--plain);
  }
}

.overlay-header {
  margin-bottom: 20px;
  padding: 16px 16px 0 16px;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .overlay-title-container {
  }

  .overlay-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;

    &.darkMode {
      color: var(--text-neutral-strong-dark);
    }
  }

  .overlay-subtitle {
    font-size: 14px;
    color: var(--text-neutral-medium);

    &.darkMode {
      color: var(--text-neutral-medium-dark);
    }
  }

  .overlay-close-icon {
    cursor: pointer;
  }
}

.overlay-scrollable-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 16px 16px 16px;

  .generic-widget {
    margin-bottom: 16px;
  }
}
