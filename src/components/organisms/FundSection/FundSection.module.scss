@import '~@paytm-h5-common/paytm_common_ui/styles/colors';
@import '/src/commonStyles/variables.scss';

.fundSection {
  width: 100%;
  padding: 16px;
  box-sizing: border-box;

  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 8px;
  }

  .titleContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .title {
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    color: var(--text-neutral-strong);
    letter-spacing: -0.02em;
    margin: 0;
  }

  .subtitle {
    color: var(--text-neutral-weak);
    font-weight: normal;
    font-size: 12px;
    line-height: 16px;
    margin: 0;
  }

  .illustration {
    width: 66.29px;
    height: 58px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .filters {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    padding: 0 4px;
  }

  .tag {
    color: var(--text-neutral-weak);
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 0.02em;
    text-transform: uppercase;
  }

  .durationSelector {
    height: 28px;
    padding: 6px 12px;
    background: var(--Border-grey100);
    border-radius: 28px;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary);
    cursor: pointer;
    justify-content: flex-end;

    &:hover {
      background: var(--background-offset-medium);

      &.darkMode {
        background: var(--background-offset-medium-dark);
      }
    }

    span {
      @include typography(body2B2, var(--text-neutral-strong));
      line-height: 16px;
      letter-spacing: 0px;

      &.darkMode {
        color: var(--text-neutral-strong-dark);
      }
    }
  }

  .chevronButtons {
    display: flex;
    flex-direction: column;
    gap: 0;
    align-items: center;
  }

  .chevronUp,
  .chevronDown {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: auto;
    margin: 0;

    img {
      width: 16px;
      height: 16px;
      object-fit: contain;
    }
  }

  .chevronUp {
    margin-bottom: -4px;
    img {
      transform: rotate(180deg);
    }
  }

  .funds {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .footer {
    display: flex;
    justify-content: center;
    margin-top: 8px;
  }

  .viewMore {
    height: 32px;
    padding: 8px 10px 8px 12px;
    background: var(--plain);
    border-radius: 96px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-family: Inter;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0px;
    color: var(--text-neutral-strong);
    cursor: pointer;
    border: 1px solid var(--Border-grey200);

    &:hover {
      background: var(--background-offset-medium);

      &.darkMode {
        background: var(--background-offset-medium-dark);
      }
    }
  }

  .arrow {
    font-size: 12px;
    line-height: 1;
  }
}
