import React, { useMemo, useRef, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { isDarkMode } from '../../../utils/commonUtil';
import { THEMES } from '../../../utils/enums';
import styles from './index.scss';
import { CHART_CONFIG, DEFAULT_CHART_OPTIONS } from './enum';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
);

// Custom plugin to add rounded corners to bars
const roundedCornersPlugin = {
  id: 'roundedCorners',
  afterDatasetsDraw: (chart) => {
    const { ctx } = chart;
    const { datasets } = chart.data;

    // Handle Calls OI bars (dataset 0)
    chart.getDatasetMeta(0).data.forEach((bar, index) => {
      const { x, y, base, width } = bar.getProps(['x', 'y', 'base', 'width']);
      const radius = 4;
      const height = Math.abs(y - base);
      const dataValue = chart.data.datasets[0].data[index];

      if (height > 0) {
        // Clear the original bar
        ctx.clearRect(x - width / 2, Math.min(y, base), width, height);

        // Draw rounded bar
        ctx.fillStyle = datasets[0].backgroundColor;
        ctx.beginPath();
        if (ctx.roundRect) {
          if (dataValue >= 0) {
            // Positive values: round top corners
            ctx.roundRect(x - width / 2, Math.min(y, base), width, height, [
              radius,
              radius,
              0,
              0,
            ]);
          } else {
            // Negative values: round bottom corners
            ctx.roundRect(x - width / 2, Math.min(y, base), width, height, [
              0,
              0,
              radius,
              radius,
            ]);
          }
        } else {
          // Fallback for browsers that don't support roundRect
          ctx.rect(x - width / 2, Math.min(y, base), width, height);
        }
        ctx.fill();
      }
    });

    // Handle Puts OI bars (dataset 1)
    chart.getDatasetMeta(1).data.forEach((bar, index) => {
      const { x, y, base, width } = bar.getProps(['x', 'y', 'base', 'width']);
      const radius = 4;
      const height = Math.abs(y - base);
      const dataValue = chart.data.datasets[1].data[index];

      if (height > 0) {
        // Clear the original bar
        ctx.clearRect(x - width / 2, Math.min(y, base), width, height);

        // Draw rounded bar
        ctx.fillStyle = datasets[1].backgroundColor;
        ctx.beginPath();
        if (ctx.roundRect) {
          if (dataValue >= 0) {
            // Positive values: round top corners
            ctx.roundRect(x - width / 2, Math.min(y, base), width, height, [
              radius,
              radius,
              0,
              0,
            ]);
          } else {
            // Negative values: round bottom corners
            ctx.roundRect(x - width / 2, Math.min(y, base), width, height, [
              0,
              0,
              radius,
              radius,
            ]);
          }
        } else {
          // Fallback for browsers that don't support roundRect
          ctx.rect(x - width / 2, Math.min(y, base), width, height);
        }
        ctx.fill();
      }
    });
  },
};

// // Custom plugin to adjust bar spacing
// const barSpacingPlugin = {
//   id: 'barSpacing',
//   beforeDatasetsDraw: (chart) => {
//     // Adjust bar positions to make them extremely close
//     chart.getDatasetMeta(0).data.forEach((bar, index) => {
//       const meta1 = chart.getDatasetMeta(1).data[index];
//       if (meta1) {
//         const originalX0 = bar.x;
//         const originalX1 = meta1.x;
//         const centerX = (originalX0 + originalX1) / 2;
//         const spacing = 12; // or higher, instead of 5

//         // Adjust positions to be extremely close
//         bar.x = centerX - spacing;
//         meta1.x = centerX + spacing;
//       }
//     });
//   },
// };

// Register the custom plugins
ChartJS.register(roundedCornersPlugin);

const ChartJSOIChart = ({
  data = null,
  height = 200,
  config = null,
  chartOptions = DEFAULT_CHART_OPTIONS,
}) => {
  const theme = isDarkMode() ? THEMES.DARK : THEMES.LIGHT;
  const currentData = data;
  const chartConfig = useMemo(
    () => ({
      ...CHART_CONFIG,
      ...config,
    }),
    [config],
  );

  const chartData = useMemo(
    () => ({
      labels: currentData.labels,
      datasets: chartConfig.datasets.map((dataset) => ({
        label: dataset.label,
        data: currentData[dataset.key],
        backgroundColor:
          theme === THEMES.DARK ? dataset.darkColor : dataset.lightColor,
        borderColor:
          theme === THEMES.DARK ? dataset.darkColor : dataset.lightColor,
        borderWidth: 0,
        borderRadius: chartConfig.borderRadius,
        borderSkipped: false,
        barPercentage: chartConfig.barPercentage,
        categoryPercentage: chartConfig.categoryPercentage,
      })),
    }),
    [currentData, theme, chartConfig],
  );
  console.log('chartData', chartData);

  const scrollContainerRef = useRef(null);

  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollLeft =
        scrollContainerRef.current.scrollWidth;
    }
  }, [currentData.labels.length]); // re-scroll if data changes

  return (
    <div ref={scrollContainerRef} className={styles.oiChartContainer}>
      <div
        style={{
          height: `${height}px`,
          minWidth: `${currentData.labels.length * 60}px`,
        }}
      >
        <Bar data={chartData} options={chartOptions} />
      </div>
    </div>
  );
};

export default ChartJSOIChart;
