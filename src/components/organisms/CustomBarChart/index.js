import { useMemo, useRef, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { isDarkMode } from '../../../utils/commonUtil';
import { THEMES } from '../../../utils/enums';
import styles from './index.scss';
import { CHART_CONFIG, DEFAULT_CHART_OPTIONS } from './enum';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
);

// Custom plugin to add rounded corners to bars
const roundedCornersPlugin = {
  id: 'roundedCorners',
  afterDatasetsDraw: (chart) => {
    const { ctx } = chart;
    const { datasets } = chart.data;

    // Handle Calls OI bars (dataset 0)
    chart.getDatasetMeta(0).data.forEach((bar, index) => {
      const { x, y, base, width } = bar.getProps(['x', 'y', 'base', 'width']);
      const radius = 4;
      const height = Math.abs(y - base);
      const dataValue = chart.data.datasets[0].data[index];

      if (height > 0) {
        // Clear the original bar
        ctx.clearRect(x - width / 2, Math.min(y, base), width, height);

        // Draw rounded bar
        ctx.fillStyle = datasets[0].backgroundColor;
        ctx.beginPath();
        if (ctx.roundRect) {
          if (dataValue >= 0) {
            // Positive values: round top corners
            ctx.roundRect(x - width / 2, Math.min(y, base), width, height, [
              radius,
              radius,
              0,
              0,
            ]);
          } else {
            // Negative values: round bottom corners
            ctx.roundRect(x - width / 2, Math.min(y, base), width, height, [
              0,
              0,
              radius,
              radius,
            ]);
          }
        } else {
          // Fallback for browsers that don't support roundRect
          ctx.rect(x - width / 2, Math.min(y, base), width, height);
        }
        ctx.fill();
      }
    });

    // Handle Puts OI bars (dataset 1)
    chart.getDatasetMeta(1).data.forEach((bar, index) => {
      const { x, y, base, width } = bar.getProps(['x', 'y', 'base', 'width']);
      const radius = 4;
      const height = Math.abs(y - base);
      const dataValue = chart.data.datasets[1].data[index];

      if (height > 0) {
        // Clear the original bar
        ctx.clearRect(x - width / 2, Math.min(y, base), width, height);

        // Draw rounded bar
        ctx.fillStyle = datasets[1].backgroundColor;
        ctx.beginPath();
        if (ctx.roundRect) {
          if (dataValue >= 0) {
            // Positive values: round top corners
            ctx.roundRect(x - width / 2, Math.min(y, base), width, height, [
              radius,
              radius,
              0,
              0,
            ]);
          } else {
            // Negative values: round bottom corners
            ctx.roundRect(x - width / 2, Math.min(y, base), width, height, [
              0,
              0,
              radius,
              radius,
            ]);
          }
        } else {
          // Fallback for browsers that don't support roundRect
          ctx.rect(x - width / 2, Math.min(y, base), width, height);
        }
        ctx.fill();
      }
    });
  },
};

// // Custom plugin to adjust bar spacing
// const barSpacingPlugin = {
//   id: 'barSpacing',
//   beforeDatasetsDraw: (chart) => {
//     // Adjust bar positions to make them extremely close
//     chart.getDatasetMeta(0).data.forEach((bar, index) => {
//       const meta1 = chart.getDatasetMeta(1).data[index];
//       if (meta1) {
//         const originalX0 = bar.x;
//         const originalX1 = meta1.x;
//         const centerX = (originalX0 + originalX1) / 2;
//         const spacing = 12; // or higher, instead of 5

//         // Adjust positions to be extremely close
//         bar.x = centerX - spacing;
//         meta1.x = centerX + spacing;
//       }
//     });
//   },
// };

// Register the custom plugins
ChartJS.register(roundedCornersPlugin);

const ChartJSOIChart = ({
  data = null,
  height = 200,
  config = null,
  chartOptions = DEFAULT_CHART_OPTIONS,
}) => {
  const theme = isDarkMode() ? THEMES.DARK : THEMES.LIGHT;
  const currentData = data;
  const chartConfig = useMemo(
    () => ({
      ...CHART_CONFIG,
      ...config,
    }),
    [config],
  );

  const chartData = useMemo(
    () => ({
      labels: currentData.labels,
      datasets: chartConfig.datasets.map((dataset) => ({
        label: dataset.label,
        data: currentData[dataset.key],
        backgroundColor:
          theme === THEMES.DARK ? dataset.darkColor : dataset.lightColor,
        borderColor:
          theme === THEMES.DARK ? dataset.darkColor : dataset.lightColor,
        borderWidth: 0,
        borderRadius: chartConfig.borderRadius,
        borderSkipped: false,
        barPercentage: chartConfig.barPercentage,
        categoryPercentage: chartConfig.categoryPercentage,
      })),
    }),
    [currentData, theme, chartConfig],
  );

  const scrollContainerRef = useRef(null);

  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollLeft =
        scrollContainerRef.current.scrollWidth;
    }
  }, [currentData.labels.length]); // re-scroll if data changes

  // Helper function to round to nearest 10,00,000 (1 million)
  const roundToNearest10Lakh = (value) => {
    if (value === 0) return 0;

    const absValue = Math.abs(value);
    const sign = value < 0 ? -1 : 1;
    const tenLakh = 1000000; // 10,00,000

    // For values less than 10 lakh, round to nearest lakh
    if (absValue < tenLakh) {
      const lakh = 100000; // 1,00,000
      return sign * Math.round(absValue / lakh) * lakh;
    }

    // For larger values, round to nearest 10 lakh (1 million)
    return sign * Math.round(absValue / tenLakh) * tenLakh;
  };

  // Calculate y-axis tick values for the fixed axis using Chart.js logic
  const getYAxisTicks = () => {
    if (!currentData || !currentData.labels.length) return [];

    const allValues = [];
    chartConfig.datasets.forEach((dataset) => {
      if (currentData[dataset.key]) {
        allValues.push(...currentData[dataset.key]);
      }
    });

    if (allValues.length === 0) return [];

    const rawMin = Math.min(...allValues, 0); // Include 0 in range
    const rawMax = Math.max(...allValues, 0);

    // Round min and max to nearest 10 lakh for cleaner scale
    const min = roundToNearest10Lakh(rawMin);
    const max = roundToNearest10Lakh(rawMax);

    // Use Chart.js-like tick calculation
    const range = max - min;
    let stepSize;
    let tickCount;

    if (range === 0) {
      return [
        {
          value: min,
          label: chartOptions.scales.y.ticks.callback
            ? chartOptions.scales.y.ticks.callback(min)
            : min.toString(),
        },
      ];
    }

    // Determine appropriate step size and tick count
    if (range < 10) {
      stepSize = 1;
      tickCount = Math.ceil(range) + 1;
    } else if (range < 100) {
      stepSize = Math.ceil(range / 6);
      // Round step size to nearest lakh for cleaner intervals
      stepSize = Math.max(100000, roundToNearest10Lakh(stepSize));
      tickCount = Math.ceil(range / stepSize) + 1;
    } else {
      // For larger ranges, use nice round numbers
      const magnitude = 10 ** Math.floor(Math.log10(range));
      const normalizedRange = range / magnitude;

      if (normalizedRange <= 1) stepSize = magnitude * 0.2;
      else if (normalizedRange <= 2) stepSize = magnitude * 0.5;
      else if (normalizedRange <= 5) stepSize = magnitude;
      else stepSize = magnitude * 2;

      // Ensure step size is rounded to nearest 10 lakh
      stepSize = roundToNearest10Lakh(stepSize);
      tickCount = Math.ceil(range / stepSize) + 1;
    }

    // Limit tick count to reasonable range
    tickCount = Math.min(Math.max(tickCount, 3), 8);

    // Recalculate step size based on final tick count for even distribution
    stepSize = range / (tickCount - 1);
    stepSize = roundToNearest10Lakh(stepSize);

    // Generate ticks from min to max with rounded values
    const ticks = [];
    for (let i = 0; i < tickCount; i += 1) {
      const rawValue = min + stepSize * i;
      const value = roundToNearest10Lakh(rawValue);
      const label = chartOptions.scales.y.ticks.callback
        ? chartOptions.scales.y.ticks.callback(value)
        : value.toFixed(0); // Use toFixed(0) for whole numbers
      ticks.push({ value, label });
    }

    // Remove duplicate values that might occur due to rounding
    const uniqueTicks = ticks.filter(
      (tick, index, arr) => index === 0 || tick.value !== arr[index - 1].value,
    );

    // Reverse to show max at top, min at bottom
    return uniqueTicks.reverse();
  };

  const yAxisTicks = getYAxisTicks();
  const chartRef = useRef(null);

  return (
    <div className={styles.oiChartWrapper}>
      <div ref={scrollContainerRef} className={styles.oiChartContainer}>
        <div
          style={{
            height: `${height}px`,
            minWidth: `${currentData.labels.length * 60}px`,
            marginRight: '60px', // Make space for fixed axis
          }}
        >
          <Bar
            ref={chartRef}
            data={chartData}
            options={{
              ...chartOptions,
              scales: {
                ...chartOptions.scales,
                y: {
                  ...chartOptions.scales.y,
                  // Keep grid lines but hide ticks and labels
                  type: 'linear',
                  ticks: {
                    ...chartOptions.scales.y.ticks,
                    display: false, // Hide tick labels
                    callback: () => '', // Return empty string for labels
                    // Use our custom tick values
                    ...(yAxisTicks.length > 0 && {
                      stepSize:
                        yAxisTicks.length > 1
                          ? Math.abs(yAxisTicks[0].value - yAxisTicks[1].value)
                          : yAxisTicks[0].value / 4,
                    }),
                  },
                  border: {
                    ...chartOptions.scales.y.border,
                    display: false, // Hide y-axis border
                  },
                  min:
                    yAxisTicks.length > 0
                      ? yAxisTicks[yAxisTicks.length - 1].value
                      : undefined,
                  max: yAxisTicks.length > 0 ? yAxisTicks[0].value : undefined,
                  // Custom tick generation to align grid lines
                  afterBuildTicks: (scale) => {
                    if (yAxisTicks.length > 0) {
                      // eslint-disable-next-line no-param-reassign
                      scale.ticks = yAxisTicks.map((tick) => ({
                        value: tick.value,
                        label: '',
                      }));
                    }
                  },
                },
              },
            }}
          />
        </div>
      </div>
      {/* Fixed right y-axis */}
      <div className={styles.fixedRightAxis}>
        <div className={styles.axisLabels}>
          {yAxisTicks.map((tick, index) => (
            <span key={index} className={styles.axisLabel}>
              {tick.label}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChartJSOIChart;
