.oiChartWrapper {
  position: relative;
  display: flex;
  width: 100%;
  background-color: transparent;
}

.oiChartContainer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  overflow-x: auto;
  background-color: transparent;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

.fixedRightAxis {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  max-width: 150px;
  background-color: var(--surface-level-1);
  border-left: 1px solid var(--border-neutral-variant);
  display: flex;
  align-items: stretch;
  justify-content: center;
  pointer-events: none;
}

.axisLabels {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  height: 100%;
  font-size: 10px;
  color: var(--text-neutral-medium);
  padding: 0px 5px 20px 5px; /* Adjust padding to align with chart area */
  box-sizing: border-box;
}

.axisLabel {
  text-align: center;
  line-height: 1;
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 12px;
}
