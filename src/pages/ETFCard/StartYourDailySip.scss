@import '/src/commonStyles/variables.scss';

.sipCardContainer {
  border-radius: 16px;
}

.root {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 16px;
  background-image: url(../../assets/etf-sip-bg.png);
  background-size: cover;
  backdrop-filter: blur(34px);
  background-color: var(--background-notice-weak);
  --etf-card-details-bg: linear-gradient(97.25deg, rgba(254, 213, 51, 0.2) 0%, rgba(245, 143, 0, 0.2) 100%);
  --etf-returns-bg: #FFFFFF99;
  --etf-title-bg: linear-gradient(92.38deg, #FED533 -0.12%, #F58F00 99.87%);
}

.darkModeRoot {
  background-color: #28220E;
  background-image: url(../../assets/etf-sip-bg-dark.png);
  --etf-card-details-bg: linear-gradient(97.25deg, rgba(241, 192, 0, 0.1) 0%, rgba(217, 122, 43, 0.1) 100%);
  --etf-returns-bg: #EEEEEE0A;
  --etf-title-bg: linear-gradient(92.38deg, #F1C000 -0.12%, #D97A2B 99.87%);
}

.header {
  display: flex;
  position: relative;
  align-self: stretch;
  align-items: center;
  justify-content: center;
}

.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .startSipBtn {
    position: relative;
    background-color: var(--background-primary-strong);
    border-radius: 8px;
    overflow: hidden;
    //z-index: 1;
    width: unset;
    margin: 0 15px;
    align-self: stretch;
    margin-bottom: 8px;
    margin-top: 24px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        110deg,
        rgba(255, 255, 255, 0) 10%,
        rgba(255, 255, 255, 0.35) 12%,
        rgba(255, 255, 255, 0.4) 14%,
        rgba(255, 255, 255, 0.35) 16%,
        rgba(255, 255, 255, 0) 18%
      );

      background-size: 200% 100%;
      animation: shine 5s infinite linear;
      z-index: 0;
      pointer-events: none;
    }

    .startSipBtnText {
      font-weight: 500;
      position: relative;
    }
  }
}

.footerTextContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 24px;
  @include typography(body3B2, var(--text-neutral-strong));
}

.dailySipContainer{
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

.equitySipTitle {
  @include typography(heading3B1, var(--text-neutral-weak));
  background-image: var(--etf-title-bg);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  /* Optional: prevent gradient flickering during animations */
  -webkit-box-decoration-break: clone;
}

.equitySipSubtitle {
  @include typography(body2B2, var(--text-neutral-strong));
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.crossIcon {
  position: absolute;
  top: 10px;
  right: 10px;
}
