// Dark mode variables
@mixin darkVars {
  // Brand
  --brandPrimary: #1a61df;
  --brandSecondary: #0a86bf;

  // Background
  --plain: #101010;
  --plainVariant: #3f3f3f;
  --popup: #1e1f21;
  --toast: #3a3a3a;
  --glass: rgba(238, 238, 238, 0.04);
  --surfaceLevel1: #202020;
  --surfaceLevel2: #303030;
  --main1: linear-gradient(to bottom, #0d1b2a, #1b263b);
  --main2: linear-gradient(to bottom, #192d42, #0f1d2c);
  // Colors
  --primaryOffset: #1c232a;
  --categoryText: #eeeeee8a;
  --primaryOffsetVariant: #364c63;
  --secondaryOffset: #534d4d;
  --secondaryOffsetVariant: #393434;
  --primary: #eeeeee;
  --neutral: #d7d7d7;
  --neutralModerate: #b6b7b9;
  --overlay: rgba(16, 16, 16, 0.85);
  --overlayLight: rgba(238, 238, 238, 0.13);
  --linksSelections: #1a61df;
  --secondary: #0a86bf;
  --secondaryLight: #0f303d;
  --positive: #02a85d;
  --positiveMedium: #0c3620;
  --positiveLight: #0e2318;
  --negative: #b74040;
  --negativeMedium: #40111b;
  --negativeLight: #281116;
  --warning: #d97a2b;
  --warningMedium: #40330c;
  --warningLight: #28220e;
  --text-neutral-strong: #eeeeee;
  --text-neutral-weak: #eeeeeeb2;
  --Border-grey200: #eeeeee3d;

  // Border
  --grey900: #eeeeee;
  --grey500: rgba(238, 238, 238, 0.54);
  --grey200: rgba(238, 238, 238, 0.24);
  --grey100: rgba(238, 238, 238, 0.12);
  --silver900: #101010;
  --silver400: rgba(16, 16, 16, 0.6);
  --silver50: rgba(16, 16, 16, 0.11);
  --offsetMedium: #1a2a36;
  --Border-grey100: #eeeeee1f;

  // --linksSelections: #1a61df; // Duplicate
  // --secondary: #0a86bf; // Duplicate
  // --positive: #02a85d; // Duplicate
  // --negative: #b74040; // Duplicate
  // --warning: #d97a2b; // Duplicate
  // --primaryOffsetVariant: #364c63; // Duplicate

  // Text
  // --grey900: #eeeeee; // Duplicate
  --grey600: rgba(238, 238, 238, 0.7);
  // --grey500: rgba(238, 238, 238, 0.54); // Duplicate
  --grey300: rgba(238, 238, 238, 0.26);
  // --silver900: #101010; // Duplicate
  // --silver400: rgba(16, 16, 16, 0.6); // Duplicate
  --silver100: rgba(16, 16, 16, 0.22);
  --silverForever: #eeeeee;
  // --linksSelections: #1a61df; // Duplicate
  --linkInverse: #101010;
  // --secondary: #0a86bf; // Duplicate
  // --positive: #02a85d; // Duplicate
  // --negative: #b74040; // Duplicate
  // --warning: #d97a2b; // Duplicate

  // Icon
  // --grey900: #eeeeee; // Duplicate
  // --grey600: rgba(238, 238, 238, 0.7); // Duplicate
  // --grey500: rgba(238, 238, 238, 0.54); // Duplicate
  // --grey300: rgba(238, 238, 238, 0.26); // Duplicate
  // --silver900: #101010; // Duplicate
  // --silver400: rgba(16, 16, 16, 0.6); // Duplicate
  // --silver100: rgba(16, 16, 16, 0.22); // Duplicate
  // --silverForever: #eeeeee; // Duplicate
  // --linksSelections: #1a61df; // Duplicate
  // --linkInverse: #101010; // Duplicate
  // --secondary: #0a86bf; // Duplicate
  // --positive: #02a85d; // Duplicate
  // --negative: #b74040; // Duplicate
  // --warning: #d97a2b; // Duplicate

  // Vivid
  --yellow: #786000;
  --yellowForever: #f1c000;
  --yellowLight: #433400;
  --bule: #2361b2;
  --blueLight: #0f2840;
  --turquoise: #0f928a;

  // Gradient
  --gradientPrimary: #1a344d;
  --gradientSecondary: #2d2d2d;
  --gradientPurpleLight: #1c1157;
  --gradientBlueLight: #001128;

  //Shimmer
  --shimmer: linear-gradient(
    to right,
    rgba(239, 241, 243, 75%) 2%,
    rgba(226, 226, 226, 30%) 25%,
    rgba(239, 241, 243, 70%) 60%
  );

  //Border
  --border-neutral-variant: rgba(16, 16, 16, 0.1294117647);
}
